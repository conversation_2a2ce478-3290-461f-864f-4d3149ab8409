/**
 * This file contains the prompt template for flashcard generation.
 * It exports a function that returns the prompt with injected settings.
 */

import { UserSettings } from '@/types';

/**
 * Returns detailed instructions based on the user's requested detail level
 */
export const getDetailLevelInstructions = (detailLevel: number): string => {
  // Using the same thresholds as the UI
  if (detailLevel <= 25) {
    return "Keep answers concise and to the point. Focus on core concepts without elaboration. Aim for 1 short sentence per answer. Keep it short enough to fit on a flashcard.";
  } else if (detailLevel <= 75) {
    return "Provide balanced answers with moderate detail. Include key concepts and brief supporting information. Aim for 1-2 sentences per answer. Keep it short enough to fit on a flashcard.";
  } else {
    return "Create comprehensive, detailed answers. Include context and supporting information where relevant. Aim for 2-3 sentences per answer. Keep it short enough to fit on a flashcard.";
  }
};

/**
 * Constructs the flashcard generation prompt with user settings
 */
export const buildFlashcardPrompt = (
  fileUrl: string,
  settings: UserSettings
): string => {
  const detailInstructions = getDetailLevelInstructions(settings.answerDetail);

  return `You are an expert educator skilled at creating comprehensive flashcards from learning material.

CRITICAL INSTRUCTION: You must ONLY use information that is explicitly present in the provided document. DO NOT use your general knowledge about the topic. DO NOT add information that is not directly stated or clearly implied in the source material.

Analyze the provided document and extract key concepts, definitions, important facts, processes, and relationships that are explicitly mentioned in the content.
Generate a thorough set of flashcards that cover ONLY the information present in the provided content.

Here is the file to analyze:

${fileUrl}

STRICT CONTENT ADHERENCE RULES:
- EVERY question and answer must be based SOLELY on information explicitly present in the provided document
- DO NOT supplement with general knowledge about the topic, even if it seems relevant
- When asking forming a question DO NOT add phrases like "according to the document", but simply ask the question as it is presented in the document
- DO NOT assume or infer information that is not clearly stated in the source material
- If the document lacks sufficient information to create a complete answer, DO NOT create that flashcard
- When in doubt about whether information is in the document, DO NOT include it

Please follow these guidelines:
1. CREATE A DESCRIPTIVE TITLE for the flashcard set based on the main topic or subject of the content as presented in the document.
2. GENERATE BETWEEN ${settings.cardRange.min} AND ${settings.cardRange.max} FLASHCARDS based on content complexity and the amount of explicit information available in the document.
3. For each topic covered in the document, create multiple questions that approach the concept from different angles, but ONLY using information explicitly provided in the source material.
4. Include both foundational concepts and more advanced or detailed information that are clearly explained in the document. ABSOLUTELY NEVER generate questions using information not present in the provided document.
5. ENSURE COMPLETE COVERAGE of the entire spectrum of content in the document - don't skip any significant topics or sections that have sufficient detail, except for meta-questions like "What is the purpose of this module?" or other generic questions about the material itself.
6. ANSWER DETAIL LEVEL: ${detailInstructions} - but remember, all details must come from the source document only.
7. Adapt your answer style to the subject matter as presented in the document:
   - For technical subjects: Include precise terminology, formulas, and step-by-step explanations ONLY as they appear in the document
   - For humanities: Provide context, examples, and interpretations ONLY as they are presented in the source material
   - For scientific topics: Explain underlying mechanisms and cite evidence ONLY as described in the document
8. When the document includes examples, analogies, or applications, you may include these to reinforce understanding, but NEVER add your own examples not present in the source.
9. AVOID meta-questions like "What is the purpose of this module?" or other generic questions about the material itself - focus exclusively on substantive content and learning outcomes that are explicitly covered in the document.
11. STRICTLY return only the JSON object. DO NOT include any markdown formatting, code fences (like \`\`\`), or any other leading or trailing text.

Return a JSON object with the format: {"title": "Descriptive title for the set", "flashcards": [{"question": "question text", "answer": "detailed answer text"}, ...]}.`;
};

/**
 * Safety settings for the AI model
 */
export const getFlashcardSafetySettings = () => [
  {
    category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
    threshold: 'BLOCK_ONLY_HIGH',
  },
  {
    category: 'HARM_CATEGORY_HATE_SPEECH',
    threshold: 'BLOCK_ONLY_HIGH',
  },
]; 