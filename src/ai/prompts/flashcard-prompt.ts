/**
 * This file contains the prompt template for flashcard generation.
 * It exports a function that returns the prompt with injected settings.
 */

import { UserSettings } from '@/types';

/**
 * Returns detailed instructions based on the user's requested detail level
 */
export const getDetailLevelInstructions = (detailLevel: number): string => {
  // Using the same thresholds as the UI
  if (detailLevel <= 25) {
    return "Keep answers concise and to the point. Focus on core concepts without elaboration. Aim for 1 short sentence per answer. Keep it short enough to fit on a flashcard.";
  } else if (detailLevel <= 75) {
    return "Provide balanced answers with moderate detail. Include key concepts and brief supporting information. Aim for 1-2 sentences per answer. Keep it short enough to fit on a flashcard.";
  } else {
    return "Create comprehensive, detailed answers. Include context and supporting information where relevant. Aim for 2-3 sentences per answer. Keep it short enough to fit on a flashcard.";
  }
};

/**
 * Constructs the flashcard generation prompt with user settings
 */
export const buildFlashcardPrompt = (
  fileUrl: string,
  settings: UserSettings
): string => {
  const detailInstructions = getDetailLevelInstructions(settings.answerDetail);
  
  return `You are an expert educator skilled at creating comprehensive flashcards from learning material.

Analyze the provided document and extract key concepts, definitions, important facts, processes, and relationships.
Generate a thorough set of flashcards that cover the essential information in the content.

Here is the file to analyze:

${fileUrl}

Please follow these guidelines:
1. CREATE A DESCRIPTIVE TITLE for the flashcard set based on the main topic or subject of the content.
2. GENERATE BETWEEN ${settings.cardRange.min} AND ${settings.cardRange.max} FLASHCARDS based on content complexity.
3. For each topic, create multiple questions that approach the concept from different angles
4. Include both foundational concepts and more advanced or detailed information. STRICTLY ONLY generate questions from the content of the provided document.
5. ENSURE COMPLETE COVERAGE of the entire spectrum of content in the document - don't skip any significant topics or sections except for meta-questions like "What is the purpose of this module?" or other generic questions about the material itself
6. ANSWER DETAIL LEVEL: ${detailInstructions}
7. Adapt your answer style to the subject matter:
   - For technical subjects: Include precise terminology, formulas, and step-by-step explanations
   - For humanities: Provide context, examples, and nuanced interpretations
   - For scientific topics: Explain underlying mechanisms and cite evidence where relevant
8. When appropriate, include examples, analogies, or applications to reinforce understanding
9. AVOID meta-questions like "What is the purpose of this module?" or other generic questions about the material itself - focus exclusively on substantive content and learning outcomes
10. STRICTLY return only the JSON object. DO NOT include any markdown formatting, code fences (like \`\`\`), or any other leading or trailing text.

Return a JSON object with the format: {"title": "Descriptive title for the set", "flashcards": [{"question": "question text", "answer": "detailed answer text"}, ...]}.`;
};

/**
 * Safety settings for the AI model
 */
export const getFlashcardSafetySettings = () => [
  {
    category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
    threshold: 'BLOCK_ONLY_HIGH',
  },
  {
    category: 'HARM_CATEGORY_HATE_SPEECH',
    threshold: 'BLOCK_ONLY_HIGH',
  },
]; 