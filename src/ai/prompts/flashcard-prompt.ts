/**
 * This file contains the prompt template for flashcard generation.
 * It exports a function that returns the prompt with injected settings.
 */

import { UserSettings } from '@/types';

/**
 * Returns detailed instructions based on the user's requested detail level
 */
export const getDetailLevelInstructions = (detailLevel: number): string => {
  // Using the same thresholds as the UI
  if (detailLevel <= 25) {
    return "Keep answers concise and to the point. Focus on core concepts without elaboration. Aim for 1 short sentence per answer. Keep it short enough to fit on a flashcard.";
  } else if (detailLevel <= 75) {
    return "Provide balanced answers with moderate detail. Include key concepts and brief supporting information. Aim for 1-2 sentences per answer. Keep it short enough to fit on a flashcard.";
  } else {
    return "Create comprehensive, detailed answers. Include context and supporting information where relevant. Aim for 2-3 sentences per answer. Keep it short enough to fit on a flashcard.";
  }
};

/**
 * Constructs the flashcard generation prompt with user settings
 */
export const buildFlashcardPrompt = (
  fileUrl: string,
  settings: UserSettings
): string => {
  const detailInstructions = getDetailLevelInstructions(settings.answerDetail);

  return `You are an expert educator skilled at creating comprehensive flashcards from learning material.

CRITICAL INSTRUCTION: You must ONLY use information that is explicitly present in the provided document. DO NOT use your general knowledge about the topic. DO NOT add information that is not directly stated or clearly implied in the source material.

Your goal is to create a comprehensive study tool that covers ALL the content in the document, not just the main topic. Think of this as creating flashcards for someone who needs to study and remember EVERYTHING in the document.

Analyze the provided document systematically and extract ALL key concepts, definitions, important facts, processes, relationships, examples, dates, names, statistics, and details that are explicitly mentioned in the content.

Here is the file to analyze:

${fileUrl}

COMPREHENSIVE COVERAGE STRATEGY:
- Read through the ENTIRE document systematically from beginning to end
- Identify ALL distinct topics, subtopics, concepts, and details covered in the document
- Do NOT focus only on the main topic - create questions about ALL content areas covered
- If the document covers multiple subjects or areas, create questions for ALL of them
- Include questions about supporting details, examples, case studies, statistics, dates, names, and specific facts mentioned
- Cover both primary concepts AND secondary information that appears in the document

STRICT CONTENT ADHERENCE RULES:
- EVERY question and answer must be based SOLELY on information explicitly present in the provided document
- DO NOT supplement with general knowledge about the topic, even if it seems relevant
- When forming a question DO NOT add phrases like "according to the document", but simply ask the question as it is presented in the document
- DO NOT assume or infer information that is not clearly stated in the source material
- If the document lacks sufficient information to create a complete answer, DO NOT create that flashcard
- When in doubt about whether information is in the document, DO NOT include it

Please follow these guidelines:
1. CREATE A DESCRIPTIVE TITLE for the flashcard set that reflects the comprehensive nature of the content covered in the document.
2. GENERATE BETWEEN ${settings.cardRange.min} AND ${settings.cardRange.max} FLASHCARDS to comprehensively cover ALL the content in the document, not just the main topic.
3. SYSTEMATIC COVERAGE: Create questions about every significant piece of information in the document:
   - Main concepts and theories
   - Supporting details and examples
   - Specific facts, dates, names, and statistics
   - Processes and procedures described
   - Relationships and connections explained
   - Case studies or scenarios presented
   - Any other substantive content mentioned
4. DISTRIBUTE questions across ALL content areas covered in the document - avoid clustering all questions around just the main topic.
5. For each area of content, create questions that approach the information from different angles, but ONLY using information explicitly provided in the source material.
6. ANSWER DETAIL LEVEL: ${detailInstructions} - but remember, all details must come from the source document only.
7. Adapt your answer style to the subject matter as presented in the document:
   - For technical subjects: Include precise terminology, formulas, and step-by-step explanations ONLY as they appear in the document
   - For humanities: Provide context, examples, and interpretations ONLY as they are presented in the source material
   - For scientific topics: Explain underlying mechanisms and cite evidence ONLY as described in the document
8. When the document includes examples, analogies, or applications, you may include these to reinforce understanding, but NEVER add your own examples not present in the source.
9. AVOID meta-questions like "What is the purpose of this module?" or other generic questions about the material itself - focus exclusively on substantive content that appears in the document.
10. STRICTLY return only the JSON object. DO NOT include any markdown formatting, code fences (like \`\`\`), or any other leading or trailing text.

Return a JSON object with the format: {"title": "Descriptive title for the set", "flashcards": [{"question": "question text", "answer": "detailed answer text"}, ...]}.`;
};

/**
 * Safety settings for the AI model
 */
export const getFlashcardSafetySettings = () => [
  {
    category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
    threshold: 'BLOCK_ONLY_HIGH',
  },
  {
    category: 'HARM_CATEGORY_HATE_SPEECH',
    threshold: 'BLOCK_ONLY_HIGH',
  },
]; 